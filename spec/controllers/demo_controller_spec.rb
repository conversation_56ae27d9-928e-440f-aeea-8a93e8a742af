require "rails_helper"

RSpec.describe DemoController do
  include Dry::Monads[:result]
  include <PERSON><PERSON><PERSON><PERSON>
  let(:account) { FactoryBot.create(:demo_account) }
  let(:jwt_sid) { "session-456" }

  let(:logged_user) { FactoryBot.create(:person, account: account) }

  def verify_user_data_cookies(user)
    expected_user_info = {
      locale: user.locale,
      employee_aggregate_id: user.aggregate_id,
      account_aggregate_id: user.account.aggregate_id
    }.to_json

    encoded_info_without_padding = ::Base64.urlsafe_encode64(expected_user_info, padding: false)

    expect(response.cookies["cultureamp.#{env_name}.user-data"]).to eq(encoded_info_without_padding)
    expect(response.headers["Set-Cookie"]).to include("Domain=#{get_root_domain}")
  end

  shared_examples_for "fusionauth cookie tests" do
    let(:jwt) { "test_jwt" }
    let(:refresh_token) { "test_refresh" }
    let(:tokens) { {jwt: jwt, refresh_token: refresh_token} }
    let(:passwordless_login) { instance_double(Authentication::Commands::FusionAuthPasswordlessLogin) }
    let(:passwordless_result) { Success(jwt: jwt, refresh_token: refresh_token) }

    context "with post sign-in feature flag enabled" do
      before do
        allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
        allow(Authentication::Commands::FusionAuthPasswordlessLogin).to receive(:new).and_return(passwordless_login)
        allow(passwordless_login).to receive(:call).and_return(passwordless_result)
      end

      it "sets fusionauth cookies with correct attributes" do
        subject

        jwt_cookie = response.cookies["cultureamp.#{env_name}.token"]
        refresh_cookie = response.cookies["cultureamp.#{env_name}.refresh-token"]

        expect(jwt_cookie).to eq(jwt)
        expect(refresh_cookie).to eq(refresh_token)
      end
    end

    context "with post sign-in feature flag disabled" do
      it "does not set fusionauth cookies" do
        allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)

        subject

        expect(response.cookies["cultureamp.#{env_name}.token"]).to be_nil
        expect(response.cookies["cultureamp.#{env_name}.refresh-token"]).to be_nil
      end
    end
  end

  describe "#index" do
    context "account is a demo account, has a demo user and user is logged in" do
      let!(:demo_user) do
        Person.create!(
          account: account,
          name: "demo user",
          email: account.demo_email,
          password: "password!!"
        )
      end

      before do
        account.assign_config!(Configs::DEMONSTRABLE, true)
        account.update(region: Region::TRIAL)
        sign_in logged_user
      end

      it "switches logged user to demo user and sets user data cookies" do
        request.host = "#{account.subdomain}.cultureamp.com"
        allow(controller).to receive(:sign_in)

        get :index

        expect(response.status).to eq(200)
        expect(controller).to have_received(:sign_in).with(demo_user)
        verify_user_data_cookies(demo_user)
      end

      context "when fusionauth authenticating successfully" do
        let(:subject) { get :index }

        before do
          request.host = "#{account.subdomain}.cultureamp.com"
          allow(controller).to receive(:sign_in)
        end

        it_behaves_like "fusionauth cookie tests"
      end
    end

    context "user is not logged in" do
      it "redirects to sign in" do
        get :index

        expect(response).to redirect_to "#{session_sign_in_path}?redirect=%2Fdemo"
      end
    end

    context "subdomain on host is not of a demo account" do
      before do
        sign_in logged_user
        account.assign_config!(Configs::DEMONSTRABLE, false)
      end

      it "retuns a 404" do
        request.host = "#{account.subdomain}.cultureamp.com"

        get :index

        expect(response.status).to eq(404)
      end
    end

    context "account has no demo user" do
      before do
        sign_in logged_user
        account.assign_config!(Configs::DEMONSTRABLE, true)
        Person.where(email: account.demo_email).delete
      end

      it "retuns a 404" do
        request.host = "#{account.subdomain}.cultureamp.com"

        get :index

        expect(response.status).to eq(404)
      end
    end
  end

  describe "#redirect" do
    context "account is a demo account, has a demo user and user is logged in" do
      let!(:demo_user) do
        Person.create!(
          account: account,
          name: "demo user",
          email: account.demo_email,
          password: "password!!"
        )
      end

      before do
        account.assign_config!(Configs::DEMONSTRABLE, true)
        account.update(region: Region::TRIAL)
        sign_in logged_user
      end

      it "switches logged user to demo user, sets user data cookies, and redirects to home page" do
        request.host = "#{account.subdomain}.cultureamp.com"
        allow(controller).to receive(:sign_in)

        get :redirect

        expect(response).to redirect_to "/"
        expect(controller).to have_received(:sign_in).with(demo_user)
        verify_user_data_cookies(demo_user)
      end

      context "when authenticating fusionauth and redirecting successfully" do
        let(:subject) { get :redirect }

        before do
          request.host = "#{account.subdomain}.cultureamp.com"
          allow(controller).to receive(:sign_in)
        end

        it_behaves_like "fusionauth cookie tests"
      end
    end

    context "user is not logged in" do
      it "redirects to sign in, with a redirect back to the demo flow" do
        get :redirect

        expect(response).to redirect_to "#{session_sign_in_path}?redirect=%2Fdemo"
      end
    end

    context "subdomain on host is not of a demo account" do
      before do
        sign_in logged_user
        account.assign_config!(Configs::DEMONSTRABLE, false)
        account.update(region: Region::TRIAL)
      end

      it "retuns a 404" do
        request.host = "#{account.subdomain}.cultureamp.com"

        get :redirect

        expect(response.status).to eq(404)
      end
    end

    context "account has no demo user" do
      before do
        sign_in logged_user
        account.assign_config!(Configs::DEMONSTRABLE, true)
        account.update(region: Region::TRIAL)
        Person.where(email: account.demo_email).delete
      end

      it "retuns a 404" do
        request.host = "#{account.subdomain}.cultureamp.com"

        get :redirect

        expect(response.status).to eq(404)
      end
    end
  end

  describe "#demo_as_employee" do
    let(:current_account) { account }
    let(:name) { "mackenzie-parker" }
    let(:user_name) { "Mackenzie Parker" }
    let!(:demo_user) { FactoryBot.create(:person, name: user_name, account: current_account) }

    before do
      allow(controller).to receive(:current_user).and_return(logged_user)
      allow(controller).to receive(:account_from_subdomain).and_return(current_account)
    end

    context "when user is not logged in" do
      before { allow(controller).to receive(:current_user).and_return(nil) }

      it "redirects to sign in" do
        get :demo_as_employee, params: {name: name}
        expect(response).to redirect_to "#{session_sign_in_path}?redirect=%2Fdemo%2Fuser%3Fname%3Dmackenzie-parker"
      end
    end

    context "when current account is not PDE region or subdomain is not cultureamp" do
      before do
        allow(current_account).to receive(:region).and_return("other_region")
      end

      it "returns a 404" do
        get :demo_as_employee, params: {name: name}

        expect(response.status).to eq(404)
      end
    end

    context "when demo user is found" do
      it "signs in as demo user, sets user data cookies, and renders index" do
        expect(Users::Queries::FindByNameForAccount).to receive(:new).and_return(double(call: demo_user))
        expect(controller).to receive(:sign_in).with(demo_user)

        get :demo_as_employee, params: {name: name}

        expect(response).to render_template(:index)
        verify_user_data_cookies(demo_user)
      end
    end

    context "when demo user is found and authenticating fusionauth successfully" do
      let(:subject) { get :demo_as_employee, params: {name: name} }

      before do
        allow(Users::Queries::FindByNameForAccount).to receive(:new).and_return(double(call: demo_user))
        expect(controller).to receive(:sign_in).with(demo_user)
      end

      it_behaves_like "fusionauth cookie tests"
    end

    context "when demo user is not found" do
      before do
        allow(Users::Queries::FindByNameForAccount).to receive(:new).and_return(double(call: nil))
      end

      it "returns a 404" do
        get :demo_as_employee, params: {name: name}

        expect(response.status).to eq(404)
      end
    end
  end

  describe "#demo in PDE region" do
    let(:current_account) { account }
    let(:name) { "mackenzie-parker" }
    let(:user_name) { "Mackenzie Parker" }
    let!(:demo_user) { FactoryBot.create(:person, name: user_name, account: current_account) }

    before do
      allow(controller).to receive(:current_user).and_return(logged_user)
      allow(controller).to receive(:account_from_subdomain).and_return(current_account)
    end

    context "when demo user is found" do
      it "signs in as demo user and renders index" do
        expect(Users::Queries::FindByNameForAccount).to receive(:new).and_return(double(call: demo_user))
        expect(controller).to receive(:sign_in).with(demo_user)

        get :index

        expect(response).to render_template(:index)
        verify_user_data_cookies(demo_user)
      end
    end

    context "when demo user is not passed in the right format ie. firstname-lastname" do
      let(:name) { "mackenzieparker" }

      it "returns a 404" do
        get :demo_as_employee, params: {name: name}

        expect(response.status).to eq(404)
      end
    end

    context "when demo user is not found" do
      let(:name) { "super-user" }

      it "returns a 404" do
        get :demo_as_employee, params: {name: name}

        expect(response.status).to eq(404)
      end
    end
  end
end
