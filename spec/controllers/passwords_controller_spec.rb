require "rails_helper"
require "spec_access_helper"

RSpec.describe PasswordsController do
  before { @request.env["devise.mapping"] = Devise.mappings[:user] }

  let(:account) { FactoryBot.create(:account) }
  let(:token) { "xyzabc" }
  let(:user) do
    FactoryBot.create(
      :user,
      account: account,
      sign_in_count: sign_in_count,
      failed_attempts: failed_attempts
    )
  end
  let(:sign_in_count) { 0 }
  let(:failed_attempts) { 0 }

  include_examples "a trackable controller"

  describe "#new" do
    subject { get :new }

    context "when not logged in" do
      it "asks for your email address" do
        subject
        expect(response).to render_template("new")
      end
    end

    it_behaves_like "a trackable action", feature: :authentication do
      let(:action) { subject }
    end
  end

  describe "#edit" do
    let(:params) { {redirect: redirect, reset_password_token: token} }
    before { get :edit, params: params }

    context "when redirecting to relative urls" do
      let(:redirect) { "/surveys" }
      it "allows the redirects" do
        expect(session[:return_to]).to eq("/surveys")
      end
    end

    context "when redirecting outside culture amp" do
      let(:redirect) { "http://www.malacious.com" }
      it "does not allow the redirect" do
        expect(session[:return_to]).to eq("/")
      end
    end

    context "when redirect loops" do
      let(:redirect) { "/session/password/edit" }
      it "does not allow the redirect" do
        expect(session[:return_to]).to be_nil
      end
    end

    context "when logged in already" do
      let(:redirect) { "/surveys/123?what=sit" }
      before do
        sign_in user
        get :edit, params: params
      end

      it "redirects immediately" do
        expect(response).to redirect_to(redirect)
      end
    end

    context "when password token is invalid" do
      let(:redirect) { "/surveys/123?what=sit" }
      let(:params) { {redirect: redirect, reset_password_token: "invalid_token"} }
      before do
        get :edit, params: params
      end

      it "redirects to the new password page" do
        expect(session[:return_to]).to eq(redirect)
        expect(response).to redirect_to("/session/password/new")
      end
    end

    context "when token has expired" do
      let(:redirect) { "/surveys" }
      let(:token) do
        # generate an expired token
        token, enc = Devise.token_generator.generate(User, :reset_password_token)
        user.reset_password_token = enc
        user.reset_password_sent_at = 1.day.ago
        user.save!

        token
      end

      it "redirects to the invalid token page" do
        expect(response).to redirect_to("/session/password/expired_link?reset_password_token=#{token}")
      end
    end
  end

  describe "#create" do
    subject { post :create, params: params }

    context "for a user that doesn't exist in the platform" do
      let(:params) { {user: {email: "<EMAIL>"}} }

      context "the datacentre is production US" do
        context "the subdomain is id or identity" do
          it "doesnt send an email and redirects to the reset password instructions page" do
            @request.host = "id.example.com"

            expect(ResetPasswordMailer).not_to receive(:delay)
            expect(Jobs::AnalyticsJob).not_to receive(:enqueue)

            subject

            expect(response).to redirect_to("/session/password/reset_instructions")
          end
        end

        context "the subdomain is not id or identity" do
          it "doesn't send an email and redirects to the reset password instructions page" do
            @request.host = "noaccount.example.com"

            expect(ResetPasswordMailer).not_to receive(:delay)
            expect(Jobs::AnalyticsJob).not_to receive(:enqueue)

            subject

            expect(response).to redirect_to("/session/password/reset_instructions")
          end
        end
      end

      context "the datacentre is not production US" do
        context "the subdomain is id or identity" do
          it "doesnt send an email and redirects to the reset password instructions page" do
            @request.host = "id.au.example.com"

            expect(ResetPasswordMailer).not_to receive(:delay)
            expect(Jobs::AnalyticsJob).not_to receive(:enqueue)

            subject

            expect(response).to redirect_to("/session/password/reset_instructions")
          end
        end
        context "the subdomain is not id or identity" do
          it "doesn't send an email and redirects to the reset password instructions page" do
            @request.host = "noaccount.eu.example.com"

            expect(ResetPasswordMailer).not_to receive(:delay)
            expect(Jobs::AnalyticsJob).not_to receive(:enqueue)

            subject

            expect(response).to redirect_to("/session/password/reset_instructions")
          end
        end
      end
    end

    context "when the user exists" do
      context "when the subdomain aware flag is enabled" do
        before do
          allow_any_instance_of(FeatureFlags::Queries::ValueForContext)
            .to receive(:call)
            .with(hash_including(flag_name: FeatureFlags::Flags::SUBDOMAIN_AWARE_LOGIN_ENABLED))
            .and_return(true)
          allow_any_instance_of(FeatureFlags::Queries::ValueForContext)
            .to receive(:call)
            .with(hash_including(flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN))
            .and_return(false)
        end
        context "when the datacentre is production-us" do
          context "when on the can't sign in page" do
            let(:params) { {user: {email: user.email}} }
            let(:delayed_mailer) { double "reset_password_mailer" }

            context "dev environment and fake-subdomain is provided" do
              let(:params) {
                {user: {email: user.email},
                 "fake-subdomain": account.subdomain}
              }

              it "sends an email to correct user and redirects to the reset password instructions page" do
                @request.host = "noaccount.example.com"

                expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
                expect(delayed_mailer).to receive(:reset_password).with(user, anything)
                expect(Jobs::AnalyticsJob).to receive(:enqueue)

                subject

                expect(response).to redirect_to("/session/password/reset_instructions")
              end
            end

            context "the subdomain is id or identity" do
              context "the user is in an account" do
                it "sends an email to correct user and redirects to the reset password instructions page" do
                  @request.host = "id.example.com"

                  expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
                  expect(delayed_mailer).to receive(:reset_password).with(user, anything)
                  expect(Jobs::AnalyticsJob).to receive(:enqueue)

                  subject

                  expect(response).to redirect_to("/session/password/reset_instructions")
                end
              end
            end

            context "the subdomain is not id or identity" do
              context "the user is in an account with the given subdomain in request" do
                it "sends an email to correct user and redirects to the reset password instructions page" do
                  @request.host = "#{account.subdomain}.example.com"

                  expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
                  expect(delayed_mailer).to receive(:reset_password).with(user, anything)
                  expect(Jobs::AnalyticsJob).to receive(:enqueue)

                  subject

                  expect(response).to redirect_to("/session/password/reset_instructions")
                end
              end
            end
          end
        end

        context "when the datacentre is not production-us" do
          context "when on the can't sign in page" do
            let(:params) { {user: {email: user.email}} }
            let(:delayed_mailer) { double "reset_password_mailer" }

            context "the subdomain is id or identity" do
              context "the user is in an account" do
                it "sends an email to correct user and redirects to the reset password instructions page" do
                  @request.host = "id.au.example.com"

                  expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
                  expect(delayed_mailer).to receive(:reset_password).with(user, anything)
                  expect(Jobs::AnalyticsJob).to receive(:enqueue)

                  subject

                  expect(response).to redirect_to("/session/password/reset_instructions")
                end
              end
            end

            context "the subdomain is not id or identity" do
              context "the user is in an account with the given subdomain in request" do
                it "sends an email to correct user and redirects to the reset password instructions page" do
                  @request.host = "#{account.subdomain}.eu.example.com"

                  expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
                  expect(delayed_mailer).to receive(:reset_password).with(user, anything)
                  expect(Jobs::AnalyticsJob).to receive(:enqueue)

                  subject

                  expect(response).to redirect_to("/session/password/reset_instructions")
                end
              end
            end
          end
        end
      end
      context "when the subdomain aware flag is disabled" do
        let(:params) { {user: {email: user.email}} }
        let(:delayed_mailer) { double "reset_password_mailer" }

        context "when on the can't sign in page" do
          it "sends an email and redirects to the reset password instructions page" do
            expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
            expect(delayed_mailer).to receive(:reset_password).with(user, anything)
            expect(Jobs::AnalyticsJob).to receive(:enqueue)

            subject

            expect(response).to redirect_to("/session/password/reset_instructions")
          end
        end

        context "the subdomain is present but not used request" do
          it "sends an email to correct user and redirects to the reset password instructions page" do
            @request.host = "noaccount.example.com"

            expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
            expect(delayed_mailer).to receive(:reset_password).with(user, anything)
            expect(Jobs::AnalyticsJob).to receive(:enqueue)

            subject

            expect(response).to redirect_to("/session/password/reset_instructions")
          end
        end
      end
      context "when on the expired token page" do
        let(:params) { {user: {reset_password_token: token}} }
        let(:token) do
          user.reset_token
          user.get_reset_password_token
        end
        let(:delayed_mailer) { double "reset_password_mailer" }

        it "sends an email and redirects to the reset password instructions page" do
          expect(ResetPasswordMailer).to receive(:delay).and_return(delayed_mailer)
          expect(delayed_mailer).to receive(:reset_password).with(user, anything)
          expect(Jobs::AnalyticsJob).to receive(:enqueue)

          subject

          expect(response).to redirect_to("/session/password/reset_instructions?reset_password_token=#{token}")
        end
      end
    end
  end

  describe "#update" do
    before do
      allow(Authentication::Commands::UpdateFusionAuthPassword).to receive(:new).and_return(update_password_command)
      user.reset_token
    end

    let(:reset_password_token) { user.get_reset_password_token }
    let(:update_password_command) { double :update_password_command, call: true }

    def set_password
      post :update, params: {user: {reset_password_token: reset_password_token, password: "abc123!~",
                                    password_confirmation: "abc123!~"}}
    end

    context "when user is not found" do
      before do
        post :update, params: {user: {reset_password_token: "abc", password: "abc123!~", password_confirmation:
          "abc123!~"}}
      end

      it_behaves_like "an ok response"

      it "doesn't sync the FusionAuth password" do
        expect(Authentication::Commands::UpdateFusionAuthPassword).not_to receive(:new)
      end
    end

    context "when user is found" do
      before do
        auth = AccountProperties::Authenticator.for_account(account_id: account.id)
        auth.update_attribute(:email, allow_email_login)
      end

      context "when user can set password" do
        let(:allow_email_login) { true }

        let(:failed_attempts) { 3 }
        let(:sign_in_count) { 3 }

        subject do
          post :update, params: {user: {reset_password_token: reset_password_token, password: "abc123!~", password_confirmation: "abc123!~"}}
        end

        it "sets password correctly" do
          subject
          expect(response).to redirect_to(root_url)
          expect(session[:flash]["flashes"]["notice"]).to eq("Your password was changed successfully.")
        end

        it "tracks the User Signed In event" do
          expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
            account_aggregate_id: account.aggregate_id,
            user_aggregate_id: user.aggregate_id,
            event_type: "User Signed In",
            event_properties: hash_including(
              "New password set" => true,
              "Failed attempts" => 3
            ),
            user_properties: {
              "Sign-in count" => 4
            }
          ))
          subject
        end

        it "syncs the password with FusionAuth" do
          expect(update_password_command).to receive(:call).with(user: user, password: "abc123!~")
          subject
        end

        context "when the reset token has expired" do
          let(:reset_password_token) do
            # generate an expired token
            token, enc = Devise.token_generator.generate(User, :reset_password_token)
            user.reset_password_token = enc
            user.reset_password_sent_at = 1.day.ago
            user.save!

            token
          end

          it "redirects the user to the expired token page" do
            post :update, params: {user: {reset_password_token: reset_password_token, password: "abc123!~", password_confirmation: "abc123!~"}}

            expect(response).to redirect_to("/session/password/expired_link?reset_password_token=#{reset_password_token}")
          end

          it "doesn't sync the FusionAuth password" do
            expect(Authentication::Commands::UpdateFusionAuthPassword).not_to receive(:new)
          end
        end

        context "when FusionAuth JWT post-sign-in is enabled" do
          let(:jwt) { "sample-jwt-token" }
          let(:refresh_token) { "sample-refresh-token" }
          let(:user_info_data) { {name: "Test User", email: "<EMAIL>"} }

          before do
            allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
            allow(controller).to receive(:fusionauth_passwordless_login).with(user).and_return([jwt, refresh_token])
            allow(controller).to receive(:set_fusionauth_cookies)
            allow(controller).to receive(:user_info).with(user).and_return(user_info_data)
            allow(controller).to receive(:set_user_data_cookie_by_region)
            allow(controller).to receive(:set_basic_user_cookie)
          end

          it "uses FusionAuth JWT for authentication" do
            expect(controller).to receive(:fusionauth_passwordless_login).with(user)
            expect(controller).to receive(:set_fusionauth_cookies).with(jwt: jwt, refresh_token: refresh_token)
            expect(controller).not_to receive(:sign_in)
            subject
          end

          it "sets basic and user data cookies" do
            expect(controller).to receive(:user_info).with(user).and_return(user_info_data)
            expect(controller).to receive(:set_user_data_cookie_by_region).with(user_info_data)
            expect(controller).to receive(:set_basic_user_cookie).with(user)
            subject
          end
        end

        context "when FusionAuth JWT post-sign-in is disabled" do
          let(:user_info_data) { {name: "Test User", email: "<EMAIL>"} }

          before do
            allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
            allow(controller).to receive(:user_info).with(user).and_return(user_info_data)
            allow(controller).to receive(:set_user_data_cookie_by_region)
            allow(controller).to receive(:set_basic_user_cookie)
          end

          it "uses traditional sign-in method" do
            expect(controller).not_to receive(:fusionauth_passwordless_login)
            expect(controller).not_to receive(:set_fusionauth_cookies)
            expect(controller).to receive(:sign_in).with(:user, user)
            subject
          end

          it "sets basic and user data cookies" do
            expect(controller).to receive(:user_info).with(user).and_return(user_info_data)
            expect(controller).to receive(:set_user_data_cookie_by_region).with(user_info_data)
            expect(controller).to receive(:set_basic_user_cookie).with(user)
            subject
          end
        end
      end

      context "when user cannot set password" do
        let(:allow_email_login) { false }

        it "gets redirected to signing" do
          post :update, params: {user: {reset_password_token: reset_password_token, password: "abc123!~", password_confirmation: "abc123!~"}}
          expect(response).to redirect_to(user_session_path)
          expect(session[:flash]["flashes"]["error"]).to eq("Cannot set password for this account")
        end
      end
    end
  end

  describe "#set_password" do
    let(:auth_api_password) { "AUTHENTICATION-UI-RESET-PASSWORD" }
    let(:aggregate_id) { user.aggregate_id }
    let(:username) { "cose" }
    let(:basic_auth_username_in_aws_secret) { "cose" }
    let(:basic_auth_password_in_aws_secret) { "AUTHENTICATION-UI-RESET-PASSWORD" }
    let(:password) { "newpassword" }
    let(:http_authorization) { "#{username}:#{auth_api_password}" }
    let(:basic_authorization) { "Basic #{::Base64.strict_encode64(http_authorization)}" }

    subject { put :set_password, params: {aggregate_id: aggregate_id, password: password} }

    before do
      stub_const("ENV", {"FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET" =>
                            JSON.generate({
                              username: basic_auth_username_in_aws_secret,
                              password: basic_auth_password_in_aws_secret
                            })})
      request.headers["BasicAuthorization"] = basic_authorization
    end

    context "when ENV FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET is empty" do
      before { ENV["FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET"] = "" }

      it "returns 500 error" do
        subject

        expect(response.status).to eq(500)
      end
    end

    context "when ENV FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET is nil" do
      before { ENV["FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET"] = nil }

      it "returns 500 error" do
        subject

        expect(response.status).to eq(500)
      end
    end

    context "when ENV FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET is invalid json" do
      before { ENV["FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET"] = "invalid format" }

      it "returns 500 error" do
        subject

        expect(response.status).to eq(500)
      end
    end

    context "when basic auth is blank" do
      let(:basic_authorization) { "" }

      it "should return 500" do
        subject

        expect(response.status).to eq(500)
      end
    end

    context "when basic auth is not correct" do
      let(:auth_api_password) { "invalid-password" }

      it "should return forbidden" do
        subject

        expect(response.status).to eq(403)
      end
    end

    context "when basic auth is correct" do
      context "when params is not correct" do
        let(:password) { "" }

        it "should return bad request" do
          subject

          expect(response.status).to eq(400)
        end
      end

      context "when user is not found" do
        let(:aggregate_id) { "fake-aggregate-id" }

        it "should return not found" do
          subject

          expect(response.status).to eq(404)
        end
      end

      context "when user is found" do
        it "should return ok" do
          subject

          expect(response.status).to eq(200)
        end
      end
    end
  end
end
