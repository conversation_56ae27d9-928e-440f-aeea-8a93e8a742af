require "rails_helper"
require_relative "../../support/jwt_header"

def get_expected_survey_metadata(user, admin: true)
  {
    "survey_id" => survey.id.to_s,
    "survey_aggregate_id" => survey.aggregate_id,
    "is_user_survey_administrator" => admin,
    "account_aggregate_id" => account.aggregate_id,
    "user_account_aggregate_id" => user.account.aggregate_id,
    "question_ids_to_aggregate_ids" => {
      question1._id.to_s => question1.aggregate_id,
      question2._id.to_s => question2.aggregate_id,
      office_question._id.to_s => office_question.aggregate_id
    },
    "minimum_comment_reporting_group" => 5,
    "indirect_identification_protection_level" => "individuals",
    "minimum_reporting_group" => 5,
    "survey_launched_at" => Time.new(2000, 1, 2).to_i
  }
end

RSpec.describe CommentSummaryServiceApi::V1::MetadataController do
  let(:account) do
    Account.create!(
      name: "surveys-api-test-account",
      subdomain: "surveys",
      region: Region.all.first
    )
  end

  before do
    allow_any_instance_of(ApplicationController).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
  end

  let(:survey) do
    Survey.create!(
      name: "surveys-api-test-survey",
      account: account,
      type: :engagement,
      launched_at: Time.new(2000, 1, 2),
      status: :active,
      survey_to_questions: ([question1, question2].map { |q|
        SurveyToQuestion.new(question: q)
      } + [office_stq])
    )
  end

  let(:factor) do
    Factor.create(name: "Index Factor")
  end

  let(:demographic_report) do
    Report.create!(
      survey: survey,
      name: "Demographic Report",
      description: "",
      sharing_status: "published",
      show_comments: true,
      is_hierarchy_report: false,
      show_ai_comment_summaries: true,
      filter_demographic_stq_ids: [],
      report_view: :standard,
      base_demographic_stq_id: office_stq.id
    )
  end

  let(:report_viewer) do
    Person.create!(
      name: "report viewer",
      email: "<EMAIL>",
      password: "secret!!",
      account: account,
      terms_and_conditions_accepted: true
    )
  end

  let(:office_question) { SingleSelectQuestion.create!(name: "Office") }

  let!(:melbourne) do
    FactoryBot.create(
      :select_option,
      value: "Melbourne",
      select_question: office_question
    )
  end

  let!(:sydney) do
    FactoryBot.create(
      :select_option,
      value: "Sydney",
      select_question: office_question
    )
  end

  let(:office_stq) do
    SurveyToQuestion.new(
      type: :segment,
      status: :active,
      question: office_question
    )
  end

  let(:question1) do
    Question.create!
  end

  let(:question2) do
    Question.create!
  end

  context "survey_metadata" do
    shared_examples "survey_metadata normal behaviour" do
      before do
        allow_any_instance_of(FeatureFlags::Queries::ValueForUser).to receive(:call).and_return(false)
      end

      describe "fetch metadata endpoint using aggregate id" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/metadata", params: {}, headers: jwt
        end

        it "returns expected metadata" do
          result = JSON.parse(response.body)
          expect(result).to eq(expected_metadata)
        end
      end

      describe "fetch metadata endpoint using survey id" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.id}/metadata", params: {}, headers: jwt
        end

        it "returns expected metadata" do
          result = JSON.parse(response.body)
          expect(result).to eq(expected_metadata)
        end
      end
    end

    context "for a user who is account admin" do
      let(:user) { FactoryBot.create(:admin, account: account) }
      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "survey_metadata normal behaviour"
    end

    context "for a user who is super user" do
      let(:user) { FactoryBot.create(:superuser_for_account, granted_account: account) }
      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "survey_metadata normal behaviour"
    end

    context "for a user who is survey admin" do
      let(:user) { FactoryBot.create(:survey_admin, account: account, survey: survey) }
      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "survey_metadata normal behaviour"
    end

    context "for a user who is surveys admin" do
      let(:user) { FactoryBot.create(:surveys_admin, account: account) }

      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "survey_metadata normal behaviour"
    end

    context "for a user who cannot administer the survey" do
      let(:user) do
        Person.create!(
          name: "other user",
          email: "<EMAIL>",
          password: "secret!!",
          account: account,
          terms_and_conditions_accepted: true
        )
      end

      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: false)
      end

      it_behaves_like "survey_metadata normal behaviour"
    end

    context "for a user from different account to the survey" do
      let(:user) do
        Person.create!(
          name: "other user",
          email: "<EMAIL>",
          password: "secret!!",
          account: Account.create!(
            name: "surveys-api-other-test-account",
            subdomain: "othersurveys",
            region: Region.all.first
          ),
          terms_and_conditions_accepted: true
        )
      end

      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: false)
      end

      it_behaves_like "survey_metadata normal behaviour"
    end

    context "un-logged in user" do
      describe "fetch metadata endpoint using aggregate id" do
        before do
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/metadata", params: {}, headers: nil
        end

        it "returns a 302 response, redirecting to login" do
          expect(response.status).to eq(302)
        end
      end

      describe "fetch metadata endpoint using survey id" do
        before do
          get "/comment_summary_service_api/v1/survey/#{survey.id}/metadata", params: {}, headers: nil
        end

        it "returns a 302 response, redirecting to login" do
          expect(response.status).to eq(302)
        end
      end
    end
  end

  context "report_metadata" do
    shared_examples "report_metadata normal behaviour" do
      before do
        allow_any_instance_of(FeatureFlags::Queries::ValueForUser).to receive(:call).and_return(false)
      end

      describe "fetch report metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns expected metadata, as admins can view all reports" do
          result = JSON.parse(response.body)

          expected_report_metadata =
            expected_metadata.merge({
              "anchor_select_option_id" => melbourne.id.to_s,
              "anchor_stq_id" => office_stq.id.to_s,
              "has_comment_report_access" => true
            })

          expect(result).to eq(expected_report_metadata)
        end
      end
    end

    context "un-logged in user" do
      describe "fetch metadata endpoint" do
        before do
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata", params: {}, headers: nil
        end

        it "returns a 302 response, redirecting to login" do
          expect(response.status).to eq(302)
        end
      end
    end

    context "unknown survey id" do
      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/11111111-1111-1111-1111-111111111111/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns a 404 response" do
          expect(response.status).to eq(404)
        end
      end
    end

    context "unknown report id" do
      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/beefbeefbeefbeef00000001/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns a 404 response" do
          expect(response.status).to eq(404)
        end
      end
    end

    context "unknown anchor demographic id" do
      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: "11111111-1111-1111-1111-111111111111",
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns a 404 response" do
          expect(response.status).to eq(404)
        end
      end
    end

    context "unknown anchor demographic value id" do
      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: "11111111-1111-1111-1111-111111111111"
            },
            headers: jwt
        end

        it "returns a 404 response" do
          expect(response.status).to eq(404)
        end
      end
    end

    context "partial anchor parameters" do
      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id
              # missing value
            },
            headers: jwt
        end

        it "returns a 400 (bad request) response" do
          expect(response.status).to eq(400)
        end
      end
    end

    context "request for hierarchy when not allowed" do
      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id,
              hierarchical_anchor: "true" # this is a non-hierarchical report, so this should be blocked
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "request for ai summary when summaries are off" do
      let(:no_ai_summary_report) do
        Report.create!(
          survey: survey,
          name: "No AI Summary Report",
          description: "",
          sharing_status: "published",
          show_comments: true,
          is_hierarchy_report: false,
          show_ai_comment_summaries: false,
          filter_demographic_stq_ids: [],
          report_view: :standard,
          base_demographic_stq_id: office_stq.id
        )
      end

      before do
        ReportAccessGrant.create(
          report: no_ai_summary_report,
          report_consumer: report_viewer,
          select_option_id: melbourne.id
        )
      end

      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{no_ai_summary_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "request for report where comments are off" do
      let(:expected_metadata) do
        get_expected_survey_metadata(report_viewer, admin: false)
      end

      let(:demographic_report_without_comments) do
        Report.create!(
          survey: survey,
          name: "demographic_report_without_comments",
          description: "",
          sharing_status: "published",
          show_comments: false,
          is_hierarchy_report: false,
          show_ai_comment_summaries: true,
          filter_demographic_stq_ids: [],
          report_view: :standard,
          base_demographic_stq_id: office_stq.id
        )
      end

      before do
        ReportAccessGrant.create(
          report: demographic_report_without_comments,
          report_consumer: report_viewer,
          select_option_id: melbourne.id
        )
      end

      describe "fetch metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report_without_comments.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns expected metadata with comment_access being false" do
          result = JSON.parse(response.body)

          expected_report_metadata =
            expected_metadata.merge({
              "anchor_select_option_id" => melbourne.id.to_s,
              "anchor_stq_id" => office_stq.id.to_s,
              "has_comment_report_access" => false
            })

          expect(result).to eq(expected_report_metadata)
        end
      end
    end

    context "for a report viewer without a valid grant" do
      describe "fetch report metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "for a report viewer with a valid grant, but an invalid select option" do
      before do
        ReportAccessGrant.create(
          report: demographic_report,
          report_consumer: report_viewer,
          select_option_id: melbourne.id
        )
      end

      describe "fetch report metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: sydney.demographic_value_id # trying to request sydney values
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "for a report viewer with a valid demographic report requesting results without an anchor" do
      before do
        ReportAccessGrant.create(
          report: demographic_report,
          report_consumer: report_viewer,
          select_option_id: melbourne.id
        )
      end

      describe "fetch report metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              # no anchor parameter, which tries to fetch all results
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "for a report viewer with a valid comment summary all results report" do
      let(:expected_metadata) do
        get_expected_survey_metadata(report_viewer, admin: false)
      end

      let(:all_results_report) do
        Report.create!(
          survey: survey,
          name: "all_results_report",
          description: "",
          sharing_status: "published",
          show_comments: true,
          is_hierarchy_report: false,
          show_ai_comment_summaries: true,
          show_ai_comment_comparisons: false,
          filter_demographic_stq_ids: [],
          report_view: :standard
        )
      end

      before do
        ReportAccessGrant.create(
          report: all_results_report,
          report_consumer: report_viewer
        )
      end

      describe "fetch report metadata endpoint with comment summary type" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{all_results_report.id}/metadata",
            params: {
              summary_type: "comment_summary"
            },
            headers: jwt
        end

        it "returns expected metadata" do
          result = JSON.parse(response.body)

          expected_report_metadata =
            expected_metadata.merge({
              "has_comment_report_access" => true
            })

          expect(result).to eq(expected_report_metadata)
        end
      end

      describe "fetch report metadata endpoint with no summary type" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{all_results_report.id}/metadata",
            params: {
              # no summary type
            },
            headers: jwt
        end

        it "returns expected metadata" do
          result = JSON.parse(response.body)

          expected_report_metadata =
            expected_metadata.merge({
              "has_comment_report_access" => true
            })

          expect(result).to eq(expected_report_metadata)
        end
      end

      describe "fetch report metadata endpoint with comparison summary type and show_ai_comment_comparisons false" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{all_results_report.id}/metadata",
            params: {
              summary_type: "comparison_summary"
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "for a report viewer with a valid comparison summary all results report" do
      let(:expected_metadata) do
        get_expected_survey_metadata(report_viewer, admin: false)
      end

      let(:all_results_report) do
        Report.create!(
          survey: survey,
          name: "all_results_report",
          description: "",
          sharing_status: "published",
          show_comments: true,
          is_hierarchy_report: false,
          show_ai_comment_summaries: false,
          show_ai_comment_comparisons: true,
          filter_demographic_stq_ids: [],
          report_view: :advanced
        )
      end

      before do
        ReportAccessGrant.create(
          report: all_results_report,
          report_consumer: report_viewer
        )
      end

      describe "fetch report metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{all_results_report.id}/metadata",
            params: {
              summary_type: "comparison_summary"
            },
            headers: jwt
        end

        it "returns expected metadata" do
          result = JSON.parse(response.body)

          expected_report_metadata =
            expected_metadata.merge({
              "has_comment_report_access" => true
            })

          expect(result).to eq(expected_report_metadata)
        end
      end

      describe "fetch report metadata endpoint with comment summary type and show_ai_comment_summaries false" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{all_results_report.id}/metadata",
            params: {
              summary_type: "comment_summary"
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end

      describe "fetch report metadata endpoint with no summary type and show_ai_comment_summaries false" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{all_results_report.id}/metadata",
            params: {
              # no summary type
            },
            headers: jwt
        end

        it "returns a 403 forbidden response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "for a test viewer with a report with AI summaries off" do
      let(:expected_metadata) do
        get_expected_survey_metadata(report_viewer, admin: false)
      end

      let(:all_results_report_2) do
        Report.create!(
          survey: survey,
          name: "all_results_report_2",
          description: "",
          sharing_status: "published",
          show_comments: true,
          is_hierarchy_report: false,
          show_ai_comment_summaries: false,
          show_ai_comment_comparisons: false,
          filter_demographic_stq_ids: [],
          report_view: :standard
        )
      end

      before do
        ReportAccessGrant.create(
          report: all_results_report_2,
          report_consumer: report_viewer
        )
      end

      describe "fetch report metadata endpoint" do
        before do
          # test feature flag is on
          allow_any_instance_of(FeatureFlags::Queries::ValueForUser)
            .to receive(:call)
            .with(user: report_viewer,
                  account: anything,
                  flag_name: FeatureFlags::Flags::SURVEY_REPORTING_ALL_REPORTS_TEST_COMMENT_AI_SUMMARY_OUTSIDE_ADMIN_REPORTS_ENABLE,
                  fallback_value: anything)
            .and_return(true)
        end

        before do
          allow_any_instance_of(FeatureFlags::Queries::ValueForUser)
            .to receive(:call)
            .with(
              user: report_viewer,
              flag_name: FeatureFlags::Flags::DEVELOP_ADMIN_ROLE,
              fallback_value: false
            )
            .and_return(false)
          allow_any_instance_of(FeatureFlags::Queries::ValueForUser)
            .to receive(:call)
            .with(
              user: report_viewer,
              flag_name: FeatureFlags::Flags::GOALS_ADMIN_ROLE,
              fallback_value: false
            )
            .and_return(false)
        end

        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{all_results_report_2.id}/metadata",
            params: {
              # no anchor parameter, which tries to fetch all results
            },
            headers: jwt
        end

        it "returns expected metadata" do
          result = JSON.parse(response.body)

          expected_report_metadata =
            expected_metadata.merge({
              "has_comment_report_access" => true
            })

          expect(result).to eq(expected_report_metadata)
        end
      end
    end

    context "for a report viewer with a valid viewer grant" do
      let(:expected_metadata) do
        get_expected_survey_metadata(report_viewer, admin: false)
      end

      before do
        ReportAccessGrant.create(
          report: demographic_report,
          report_consumer: report_viewer,
          select_option_id: melbourne.id
        )
      end

      describe "fetch report metadata endpoint" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: report_viewer.aggregate_id)
          get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/report/#{demographic_report.id}/metadata",
            params: {
              anchor_demographic: office_question.aggregate_id,
              anchor_demographic_value: melbourne.demographic_value_id
            },
            headers: jwt
        end

        it "returns expected metadata" do
          result = JSON.parse(response.body)

          expected_report_metadata =
            expected_metadata.merge({
              "anchor_select_option_id" => melbourne.id.to_s,
              "anchor_stq_id" => office_stq.id.to_s,
              "has_comment_report_access" => true
            })

          expect(result).to eq(expected_report_metadata)
        end
      end
    end

    context "for an account admin, viewing a report" do
      let(:user) { FactoryBot.create(:admin, account: account) }

      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "report_metadata normal behaviour"
    end

    context "for a super user, viewing a report" do
      let(:user) { FactoryBot.create(:superuser_for_account, granted_account: account) }

      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "report_metadata normal behaviour"
    end

    context "for a survey admin, viewing a report" do
      let(:user) { FactoryBot.create(:survey_admin, account: account, survey: survey) }

      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "report_metadata normal behaviour"
    end

    context "for a surveys admin, viewing a report" do
      let(:user) { FactoryBot.create(:surveys_admin, account: account) }
      let(:expected_metadata) do
        get_expected_survey_metadata(user, admin: true)
      end

      it_behaves_like "report_metadata normal behaviour"
    end
  end

  # survey_metadata_v2 test for admin reports and shared reports
  describe "GET #survey_metadata_v2" do
    before do
      mock_feature_flag(
        FeatureFlags::Flags::DEVELOP_ADMIN_ROLE,
        false
      )
      mock_feature_flag(
        FeatureFlags::Flags::GOALS_ADMIN_ROLE,
        false
      )
      mock_feature_flag(
        FeatureFlags::Flags::SURVEY_REPORTING_ADMIN_REPORTS_FULL_FILTERING_FOR_COMMENT_AI_SUMMARIES,
        true
      )
      mock_feature_flag(
        FeatureFlags::Flags::SURVEY_REPORTING_SHARED_REPORTS_FULL_FILTERING_FOR_COMMENT_AI_SUMMARIES,
        true
      )
      mock_feature_flag(
        FeatureFlags::Flags::SURVEY_REPORTING_ALL_REPORTS_TEST_COMMENT_AI_SUMMARY_OUTSIDE_ADMIN_REPORTS_ENABLE,
        true
      )
      mock_feature_flag(
        FeatureFlags::Flags::REPORT_SHARING_MULTI_DEMOGRAPHICS_REPORT_VIEWING,
        false
      )
    end

    let(:expected_full_filtering_metadata) do
      {
        "filters" => [
          {
            "demographic_mongo_id" => office_stq.id.to_s,
            "demographic_aggregate_id" => office_question.aggregate_id,
            "demographic_value_ids" => [
              {
                "demographic_value_mongo_id" => melbourne.id.to_s,
                "demographic_value_aggregate_id" => melbourne.demographic_value_id
              }
            ],
            "hierarchical" => false
          }
        ],
        "has_comment_report_access" => true,
        "hierarchy_privacy_enabled" => hierarchy_privacy_enabled
      }
    end

    let(:hierarchy_privacy_enabled) { true }

    shared_examples "survey_metadata_v2 shared report behaviour" do
      let(:hierarchy_privacy_enabled) { false }

      describe "it fetches survey_metadata_v2 with valid data" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
          get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
            params: params,
            headers: jwt
        end

        it "returns expected metadata, as admins can view all reports" do
          result = JSON.parse(response.body)
          expected_survey_v2_metadata = expected_survey_metadata.merge(expected_full_filtering_metadata)

          expect(result).to eq(expected_survey_v2_metadata)
        end
      end
    end

    shared_examples "survey_metadata_v2 admin behaviour" do
      describe "it fetches survey_metadata_v2 with valid data" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
          get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
            params: params,
            headers: jwt
        end

        it "returns expected metadata, as admins can view all reports" do
          result = JSON.parse(response.body)
          expected_survey_v2_metadata = expected_survey_metadata.merge(expected_full_filtering_metadata)

          expect(result).to eq(expected_survey_v2_metadata)
        end
      end
    end

    let(:user) { FactoryBot.create(:admin, account: account) }
    let(:expected_survey_metadata) { get_expected_survey_metadata(user, admin: true) }

    # user base check for admin reports
    context "when admin report" do
      let(:params) {
        {
          report_id: "admin",
          other_filters: "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}"
        }
      }

      context "for an account admin, accessing an admin report" do
        it_behaves_like "survey_metadata_v2 admin behaviour"
      end

      context "for a super user, accessing an admin report" do
        let(:user) { FactoryBot.create(:superuser_for_account, granted_account: account) }
        it_behaves_like "survey_metadata_v2 admin behaviour"
      end

      context "for a survey admin, accessing an admin report" do
        let(:user) { FactoryBot.create(:survey_admin, account: account, survey: survey) }
        it_behaves_like "survey_metadata_v2 admin behaviour"
      end

      context "for a report viewer, accessing an admin report" do
        let(:user) { report_viewer }
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
          get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
            params: {
              report_id: "admin",
              other_filters: "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}"
            },
            headers: jwt
        end

        it "returns a 403 response" do
          expect(response.status).to eq(403)
        end
      end
    end

    # user base check for shared reports
    context "when shared reports" do
      let(:params) {
        {
          report_id: report.id,
          anchor_filters: anchor_filters
        }
      }

      # Use office demographic report as shared report
      context "for an account admin, accessing a demographic report" do
        let(:report) { demographic_report }
        let(:anchor_filters) { "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}" }
        it_behaves_like "survey_metadata_v2 shared report behaviour"
      end

      context "for a super user, accessing a demographic report" do
        let(:user) { FactoryBot.create(:superuser_for_account, granted_account: account) }
        let(:report) { demographic_report }
        let(:anchor_filters) { "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}" }
        it_behaves_like "survey_metadata_v2 shared report behaviour"
      end

      context "for a survey admin, accessing a demographic report" do
        let(:user) { FactoryBot.create(:survey_admin, account: account, survey: survey) }
        let(:report) { demographic_report }
        let(:anchor_filters) { "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}" }
        it_behaves_like "survey_metadata_v2 shared report behaviour"
      end

      context "for a report viewer, accessing a demographic report" do
        before do
          ReportAccessGrant.create(
            report: report,
            report_consumer: report_viewer,
            select_option_id: melbourne.id
          )
        end

        let(:user) { report_viewer }
        let(:expected_survey_metadata) { get_expected_survey_metadata(user, admin: false) }
        let(:report) { demographic_report }
        let(:anchor_filters) { "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}" }
        it_behaves_like "survey_metadata_v2 shared report behaviour"
      end

      context "for a non report viewer, accessing a demographic report" do
        before do
          jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
          get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
            params: params,
            headers: jwt
        end

        let(:user) { report_viewer }
        let(:report) { demographic_report }
        let(:anchor_filters) { "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}" }
        it "returns a 403 response" do
          expect(response.status).to eq(403)
        end
      end
    end

    context "for un-logged in user" do
      before do
        get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata", params: {}, headers: nil
      end

      it "returns a 302 response, redirecting to login" do
        expect(response.status).to eq(302)
      end
    end

    context "when unknown survey id" do
      before do
        jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
        get "/comment_summary_service_api/v2/survey/11111111-1111-1111-1111-111111111111/metadata",
          params: {
            report_id: "admin",
            other_filters: "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}"
          },
          headers: jwt
      end

      it "returns a 404 response" do
        expect(response.status).to eq(404)
      end
    end

    context "when feature flags are off" do
      before do
        mock_feature_flag(
          FeatureFlags::Flags::SURVEY_REPORTING_ADMIN_REPORTS_FULL_FILTERING_FOR_COMMENT_AI_SUMMARIES,
          false
        )
        mock_feature_flag(
          FeatureFlags::Flags::SURVEY_REPORTING_SHARED_REPORTS_FULL_FILTERING_FOR_COMMENT_AI_SUMMARIES,
          false
        )
        mock_feature_flag(
          FeatureFlags::Flags::SURVEY_REPORTING_ALL_REPORTS_TEST_COMMENT_AI_SUMMARY_OUTSIDE_ADMIN_REPORTS_ENABLE,
          false
        )

        jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
        get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
          params: {
            report_id: "admin",
            other_filters: "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}"
          },
          headers: jwt
      end

      it "returns a 403 response" do
        expect(response.status).to eq(403)
      end
    end

    context "when report_id is invalid" do
      before do
        jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
        get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
          params: {
            report_id: "invalid", # this is invalid report_id
            other_filters: "#{office_question.aggregate_id}$#{melbourne.demographic_value_id}"
          },
          headers: jwt
      end

      it "returns a 404 response" do
        expect(response.status).to eq(404)
      end
    end

    context "when unknown filter demographic id" do
      before do
        jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
        get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
          params: {
            report_id: "admin",
            other_filters: "11111111-1111-1111-1111-111111111111$#{melbourne.demographic_value_id}"
          },
          headers: jwt
      end

      it "returns a 404 response" do
        expect(response.status).to eq(404)
      end
    end

    context "when unknown filter demographic value id" do
      before do
        jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
        get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
          params: {
            report_id: "admin",
            other_filters: "#{office_question.aggregate_id}$11111111-1111-1111-1111-111111111111"
          },
          headers: jwt
      end

      it "returns a 404 response" do
        expect(response.status).to eq(404)
      end
    end

    context "when invalid filters query params" do
      before do
        jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
        get "/comment_summary_service_api/v2/survey/#{survey.aggregate_id}/metadata",
          params: {
            report_id: "admin",
            other_filters: "invalid_filters" # this is invalid format of filters key values pair
          },
          headers: jwt
      end

      it "returns a 404 response" do
        expect(response.status).to eq(404)
      end
    end
  end

  describe "GET #fetch_comparison_metadata" do
    let(:user) { FactoryBot.create(:admin, account: account) }

    let(:comparison_flag_state) { true }

    let(:demographic_response) { {"demographic_data" => true} }

    let(:factor_response) { {"factor_data" => true} }

    let(:full_filtering_response) { {"full_filtering_data" => true} }

    let(:survey_response) { {"survey_data" => true} }

    before do
      mock_feature_flag(
        FeatureFlags::Flags::SURVEY_REPORTING_DEMOGRAPHICS_REPORT_SHOW_AI_COMPARISON_SUMMARY_ENABLE,
        comparison_flag_state
      )

      allow_any_instance_of(CommentSummaryService::Queries::SurveyMetadata)
        .to receive(:call)
        .and_return(survey_response)

      allow_any_instance_of(CommentSummaryService::Queries::FullFilteringMetadata)
        .to receive(:call)
        .and_return(full_filtering_response)

      allow_any_instance_of(CommentSummaryService::Queries::DemographicMetadata)
        .to receive(:call)
        .and_return(demographic_response)

      allow_any_instance_of(CommentSummaryService::Queries::FactorMetadata)
        .to receive(:call)
        .and_return(factor_response)
    end

    before do
      jwt = Support::JwtHeader.new.call(aggregate_id: user.aggregate_id)
      get "/comment_summary_service_api/v1/survey/#{survey.aggregate_id}/comparison_metadata",
        params: {
          report_id: "admin",
          factor_id: factor.aggregate_id,
          demographic_id: office_question.aggregate_id
        },
        headers: jwt
    end

    context "for a valid user request" do
      it "returns the expected combined data" do
        expect(response.status).to eq(200)
        result = JSON.parse(response.body)
        expect(result).to eq(
          {
            "demographic" => {
              "demographic_data" => true
            },
            "factor" => {
              "factor_data" => true
            },
            "full_filtering_data" => true,
            "survey_data" => true
          }
        )
      end
    end

    context "when feature flag is off" do
      let(:comparison_flag_state) { false }

      it "returns a 403 error code" do
        expect(response.status).to eq(403)
      end
    end

    context "if there is an error in the factor payload" do
      let(:factor_response) {
        {error: 404, message: "Factor could not be found"}
      }

      it "returns the error code" do
        expect(response.status).to eq(404)
      end
    end

    context "if there is an error in the full filtering payload" do
      let(:full_filtering_response) {
        {error: 404, message: "Missing User or Survey data"}
      }

      it "returns the error code" do
        expect(response.status).to eq(404)
      end
    end

    context "if there is an error in the demographic payload" do
      let(:demographic_response) {
        {error: 403, message: "One or more questions is missing english text"}
      }

      it "returns the error code" do
        expect(response.status).to eq(403)
      end
    end

    context "if there is an error in the survey metadata" do
      let(:survey_response) {
        nil
      }

      it "returns 404 error code" do
        expect(response.status).to eq(404)
      end
    end
  end
end

def mock_feature_flag(flag_name, flag_state)
  allow_any_instance_of(FeatureFlags::Queries::ValueForContext)
    .to receive(:call)
    .with(hash_including(flag_name: flag_name))
    .and_return(flag_state)
end
