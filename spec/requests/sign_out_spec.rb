require "rails_helper"
require "spec_access_helper"

RSpec.describe "session/sign_out", type: :request do
  let(:user) { FactoryBot.create(:user) }

  before do
    sign_in(user)
  end

  context "when fusionauth JWT feature flag is disabled" do
    before do
      expect_any_instance_of(SessionsController).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
    end

    it "should redirect to the Culture Amp sign-out page and revoke session access" do
      delete "/session/sign_out"

      expect(response.status).to eq(302)
      expect(response.headers["Location"]).to eq("https://www.cultureamp.com/sign_out")

      get "/my/user-settings"
      expect(response.status).to eq(302)
      expect(response.headers["Location"]).to include("/session/sign_in?redirect=")
    end
  end

  context "when fusionauth JWT feature flag is enabled" do
    before do
      expect_any_instance_of(SessionsController).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
      expect_any_instance_of(SessionsController).to receive(:sign_out_from_fusionauth)
    end

    it "returns no_content status and signs out from FusionAuth" do
      delete "/session/sign_out"
      expect(response).to have_http_status(:no_content)
    end
  end
end
