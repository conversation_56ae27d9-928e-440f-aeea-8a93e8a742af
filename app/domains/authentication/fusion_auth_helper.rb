module Authentication
  module <PERSON><PERSON><PERSON><PERSON>el<PERSON>
    def fusionauth_jwt_post_signin_enabled?(subdomain)
      FeatureFlags::Queries::ValueForContext.new.call(
        flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
        subdomain: subdomain,
        fallback_value: false
      )
    end

    def delete_fusionauth_entity(sid, account_id)
      delete_entity = Authentication::Commands::FusionAuthDeleteEntity.new
      result = delete_entity.call(entity_id: sid, account_id: account_id)

      unless result.success?
        Rails.logger.error("Failed to delete FusionAuth entity for masquerade session")
        Sentry.capture_exception(result.failure)
        raise "Failed to delete FusionAuth entity for masquerade session"
      end
    end

    def sign_out_fusionauth(user_id:, refresh_token:, global: false)
      sign_out_command = Authentication::Commands::FusionAuthSignOut.new
      result = sign_out_command.call(user_id: user_id, refresh_token: refresh_token, global: global)

      unless result.success?
        Rails.logger.error("Failed to sign out from FusionAuth")
        Sentry.capture_exception(result.failure)
        raise "Failed to sign out from FusionAuth"
      end
    end

    def fusionauth_passwordless_login(user)
      result = Authentication::Commands::FusionAuthPasswordlessLogin.new.call(
        account: user.account,
        email: user.email
      )

      if result.success?
        [result.value![:jwt], result.value![:refresh_token]]
      end
    end

    def get_subdomain_from_request
      return nil unless respond_to?(:request) && request.present?

      if respond_to?(:params) && params["fake-subdomain"].present? && !Rails.env.production?
        return params["fake-subdomain"]
      end

      if respond_to?(:params) && params["sd"].present? && !Rails.env.production?
        return params["sd"]
      end

      request.subdomains&.first
    end

    def subdomain_aware_login_enabled?(subdomain)
      FeatureFlags::Queries::ValueForContext.new.call(
        flag_name: FeatureFlags::Flags::SUBDOMAIN_AWARE_LOGIN_ENABLED,
        subdomain: subdomain,
        fallback_value: false
      )
    end

    def multi_tenants_fusionauth_client
      fusionauth_client = FusionAuth::FusionAuthClient.new(ENV["FUSION_AUTH_ENTITY_API_KEY"], ENV["FUSION_AUTH_HOST"])
      tenant_name = "#{ENV["FARM"]} - cultureamp"
      request = {
        search: {
          name: tenant_name,
          startRow: 0
        }
      }

      response = fusionauth_client.search_tenants(request)
      unless response.was_successful
        raise Authentication::FusionAuthException.new(response.exception.message, response.status)
      end

      tenants = response.success_response.tenants
      if tenants.empty?
        error_message = "Tenant not found for: #{tenant_name}"
        raise Authentication::FusionAuthException.new(error_message, 404)
      end

      if tenants.length != 1
        error_message = "Multiple tenants found for: #{tenant_name}"
        raise Authentication::FusionAuthException.new(error_message, 404)
      end

      tenant_id = tenants.first.id
      fusionauth_client.set_tenant_id(tenant_id)

      fusionauth_client
    end
  end
end
