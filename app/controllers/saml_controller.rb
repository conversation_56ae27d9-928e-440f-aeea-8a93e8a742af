require "integration"

class SamlController < ApplicationController
  include Redirectable
  include <PERSON><PERSON><PERSON><PERSON><PERSON>
  include Authentication::FusionAuthHelper

  allows_access_to :anonymous_users

  skip_before_action :verify_authenticity_token, only: [:callback]

  before_action :valid_saml_account?

  tag_requests_with feature: :authentication

  # Metadata endpoint as a Service Provider (SP)
  def metadata
    meta = OneLogin::RubySaml::Metadata.new
    render xml: meta.generate(saml_settings.for_metadata), content_type: "application/samlmetadata+xml"
  end

  def show
    track_sso_initiated_event

    req = OneLogin::RubySaml::Authrequest.new

    redirect_to req.create(saml_settings.for_request, RelayState: get_redirect)
  end

  def callback
    code, saml_user, is_valid_fusionauth_user = SAML::Authenticator.authenticate(account, saml_integration, params[:SAMLResponse])

    case code
    when :no_saml_response
      redirect_to(saml_signin_path(sd: subdomain))
    when :invalid_response, :invalid_name_id_format
      render_unauthorized
    when :invalid_user
      render_forbidden
    when :ok
      sign_in(saml_user)
      session[:created_at] = Time.current.utc

      if redirect_path_from_relay_state
        is_deep_link = true
        redirect_path = redirect_path_from_relay_state
      else
        is_deep_link = false
        redirect_path = after_sign_in_path_for(saml_user)
      end

      track_sign_in_event(
        auth_method: Analytics::AUTH_METHOD_SAML,
        is_deep_link: is_deep_link,
        auth_system: is_valid_fusionauth_user ? Analytics::AUTHENTICATION_SYSTEM_FUSION_AUTH : Analytics::AUTHENTICATION_SYSTEM_MURMUR
      )

      # Set basic user cookie if user is valid in FusionAuth
      set_basic_user_cookie(saml_user) if is_valid_fusionauth_user
      set_user_data_cookie_by_region(user_info(saml_user)) if is_valid_fusionauth_user

      # Handle JWT generation if user is valid in FusionAuth and feature flag is enabled
      jwt, refresh_token = fusionauth_passwordless_login(saml_user) if is_valid_fusionauth_user && fusionauth_jwt_post_signin_enabled?(get_subdomain_from_request)

      # Set FusionAuth JWT and refresh token cookies if available
      if jwt && refresh_token
        set_fusionauth_cookies(jwt: jwt, refresh_token: refresh_token)
      end

      redirect_to redirect_path
    else
      raise "unrecognised return code"
    end
  end

  private

  def valid_saml_account?
    case SAML::Authenticator.valid_saml_account?(account)
    when :inactive_saml
      redirect_to new_user_session_path
    when :saml_not_allowed
      flash.alert = "SAML authentication has been disabled. Please check with your Culture Amp administrator or email #{Appamp::Settings.email_support}."
      redirect_to new_user_session_path
    when :ok
      true
    end
  end

  def account
    @account ||= Account.subdomain(subdomain)
  end

  def subdomain
    @subdomain ||= params[:sd]
  end

  def saml_integration
    @saml_integration ||= account.active_saml_integration
  end

  def saml_settings
    saml_integration.parse_remote
    SAML::Settings.new(saml_integration: saml_integration, opts: extra_opts)
  end

  def extra_opts
    if account.enabled?(Flags::SAML_REPLY_URL_USE_ACCOUNT_DOMAIN)
      {
        assertion_consumer_service_url: "#{request.protocol}#{account.domain}/saml/callback/#{CGI.escape(subdomain)}",
        issuer: account.domain
      }
    else
      {
        assertion_consumer_service_url: "#{request.protocol}#{request.host_with_port}/saml/callback/#{CGI.escape(subdomain)}",
        issuer: request.host
      }
    end
  end

  def redirect_path_from_relay_state
    params[:RelayState].present? ? extract_path(params[:RelayState]) : nil
  end
end
