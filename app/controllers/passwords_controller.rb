class PasswordsController < Devise::PasswordsController
  AuthorizationHeaderError = Class.new(StandardError)
  FetchResetPasswordSecretError = Class.new(StandardError)
  FetchResetPasswordSecretPasswordError = Class.new(StandardError)

  # include Controlamp::DeprecatedModelCentric
  include Redirectable
  include <PERSON><PERSON><PERSON>per
  include Authentication::FusionAuthHelper

  component :session
  layout "plain"
  skip_before_action :verify_authenticity_token, only: [:set_password]
  before_action :redirect_if_logged_in,
    :set_return_to,
    :redirect_to_new_user_password_if_token_invalid,
    only: [:edit]
  before_action :ensure_account_allows_email_login, only: [:update]
  before_action :validate_reset_password_token_expiry, only: [:edit, :update]
  before_action :http_authenticate, only: [:set_password]

  tag_requests_with feature: :authentication

  def new
    super
  end

  # A different version of the reset password page for users who have come to the platform
  # using a reset password link that has expired. We do not required them to re-enter their
  # email, as we are able to look up who they from the token.
  def expired_link
    self.resource = resource_class.new
    resource.reset_password_token = params[:reset_password_token]
  end

  # POST /resource/password (aka send password reset email)
  # Overide the devise create password method as we are now calling this from two different
  # pages. If an email is provided, use that to locate a user, otherwise attempt to find a
  # user from the token. Always respond with the "check your inbox" page, so that we don't
  # leak information about users in our platform.
  def create
    if params["user"]["email"].present?
      subdomain = get_subdomain(request)
      # If subdomain is present we want to only send the email to users in matching acocunt.
      # If subdomain is id or identity, we want to use default devise flow.
      if use_subdomain_for_user_lookup(subdomain)
        send_reset_password_instructions_using_parameters(subdomain)
      else
        splunk_logger.log(app: "murmur",
                          module: "passwords_controller",
                          code: "user.password_reset.email.sent",
                          msg: "email sent with default devise flow")
        self.resource = resource_class.send_reset_password_instructions(resource_params)
        track_password_reset_email(resource) if resource.persisted?
      end
    else
      # If no email is provided, we are likely coming from the expired link page and we have a token specifying user
      user&.send_reset_password_instructions
      track_password_reset_email(user) if user
    end
    # We always want to show the "check your inbox" page, so that we don't leak information about users.
    respond_with({}, location: after_sending_reset_password_instructions_path_for(resource_name))
  end

  def reset_instructions
    # devise automatically sets a flash message when a password reset is attempted.
    # We are now redirecting users to a new page and we don't want to see the flash.
    flash.clear

    @resent_with_token = params["reset_password_token"].present?
  end

  def edit
    @new_user = user.sign_in_count < 1
    super
  end

  # PUT /resource/password
  def update
    self.resource = resource_class.reset_password_by_token(resource_params)
    yield resource if block_given?

    if resource.errors.empty?
      Authentication::Commands::UpdateFusionAuthPassword.new.call(user: resource, password: resource_params[:password])
      resource.cache_failed_attempts
      resource.unlock_access! if unlockable?(resource)
      if Devise.sign_in_after_reset_password
        flash_message = resource.active_for_authentication? ? :updated : :updated_not_active
        set_flash_message!(:notice, flash_message) unless free_account?
        resource.after_database_authentication

        if fusionauth_jwt_post_signin_enabled?(get_subdomain_from_request)
          jwt, refresh_token = fusionauth_passwordless_login(user)
          # Set FusionAuth JWT and refresh token cookies if available
          if jwt && refresh_token
            set_fusionauth_cookies(jwt: jwt, refresh_token: refresh_token)
            set_basic_user_cookie(user)
            set_user_data_cookie_by_region(user_info(user))
          end
        else
          sign_in(resource_name, resource)
          set_basic_user_cookie(user)
          set_user_data_cookie_by_region(user_info(user))
        end

        track_sign_in_event(auth_method: Analytics::AUTH_METHOD_PASSWORD, new_password_set: true)
      else
        set_flash_message!(:notice, :updated_not_active)
      end
      respond_with resource, location: after_resetting_password_path_for(resource)
    else
      set_minimum_password_length
      respond_with resource
    end
  end

  # PUT /private_api/set_password
  def set_password
    aggregate_id = params[:aggregate_id]
    password = params[:password]

    return render_bad_request if aggregate_id.blank? || password.blank?

    user = Person.find_employee_by_aggregate_id(aggregate_id)
    return render_not_found unless user

    user.update(password: password)

    head(:ok)
  end

  private

  def splunk_logger
    @splunk_logger ||= Splunk::Logger.new(Rails.logger)
  end

  def get_subdomain(request)
    # This query parameter is used for testing on dev farms
    # e.g. dolly.development.cultureamp.net/app/auth?fake-subdomain=hooli
    if params["fake-subdomain"].present? && !Rails.env.production?
      params["fake-subdomain"]
    elsif request.subdomain.present? && !Rails.env.staging?
      # we want to use the first part of the subdomain in case we are in AU or EU
      # e.g. identity.eu.cultureamp.com
      request.subdomain.split(".").first
    end
  end

  def user_from_account_subdomain(subdomain, email)
    return unless subdomain.present? && !%w[id identity].include?(subdomain)

    account = Account.where(subdomain: subdomain).first
    return unless account

    User.where(email: email, account_id: account.id).first
  end

  def send_reset_password_instructions_using_parameters(subdomain)
    email = params["user"]["email"]
    user_from_account = user_from_account_subdomain(subdomain, email)
    if user_from_account
      user_from_account.send_reset_password_instructions
      splunk_logger.log(app: "murmur",
                        module: "passwords_controller",
                        code: "user.password_reset.email.sent",
                        msg: "email sent to user matching email and subdomain")
      track_password_reset_email(user_from_account) if user_from_account.persisted?
    else
      splunk_logger.log(app: "murmur",
                        module: "passwords_controller",
                        code: "user.not_found.error",
                        msg: "User not found for email: #{email} and subdomain: #{subdomain}")
    end
  end

  def use_subdomain_for_user_lookup(subdomain)
    subdomain.present? && !%w[id identity].include?(subdomain) && multi_account_login_enabled?(subdomain)
  end

  def free_account?
    user&.account&.free?
  end

  def redirect_if_logged_in
    redirect_to(get_redirect) if current_user && get_redirect
  end

  def redirect_to_login_if_token_invalid
    redirect_to_login(error: @token_error) unless user
  end

  def redirect_to_new_user_password_if_token_invalid
    redirect_to_new_user_password(error: @token_error) unless user
  end

  def ensure_account_allows_email_login
    redirect_to_login(error: "Cannot set password for this account") if user && !account_allows_email_login?
  end

  def user
    @_user ||= begin
      @token_error = "No token given" if url_token.blank?
      token = Devise.token_generator.digest(self, :reset_password_token, url_token)
      user = User.where(reset_password_token: token).first if token
      @token_error = "This link has expired. Try sending a new email." if user.blank?
      user
    end
  end

  def url_token
    # different params depending on action #edit or #update
    @url_token ||= params["reset_password_token"] || params["user"]["reset_password_token"]
  end

  def account_allows_email_login?
    AccountProperties::Authenticator
      .for_account(account_id: user.account_id)
      .email_allowed?
  end

  def redirect_to_login(flash = {})
    redirect_to(user_session_path, flash: flash)
  end

  def redirect_to_new_user_password(flash = {})
    redirect_to(new_user_password_path, flash: flash)
  end

  def validate_reset_password_token_expiry
    user = User.with_reset_password_token(url_token)
    redirect_to_expired_link_page(url_token) if user && !user.reset_password_period_valid?
  end

  def redirect_to_expired_link_page(token)
    redirect_to session_password_expired_link_path(reset_password_token: token)
  end

  # This is an override of a devise method. Devise by default redirects the user to the sign-in
  # page after sending the reset password email. Now we have a new page with instructions.
  # Pass the token url param along so the instructions page knows whether or not the email
  # was sent for the first time, or a resend because of an expired token.
  def after_sending_reset_password_instructions_path_for(resource_name)
    params = url_token.present? ? {reset_password_token: url_token} : {}
    session_password_reset_instructions_path(params)
  end

  def track_password_reset_email(user)
    enqueue_amplitude_event(
      account_aggregate_id: user.account.aggregate_id,
      user_aggregate_id: user.aggregate_id,
      event_type: Analytics::EVENT_TYPE_PLATFORM_COMMUNICATION_RECEIVED,
      event_properties: {
        Analytics::EVENT_PROPERTY_MEDIUM => Analytics::MEDIUM_EMAIL,
        Analytics::EVENT_PROPERTY_COMMUNICATION_NAME => Analytics::COMMUNICATION_NAME_RESET_PASSWORD,
        Analytics::EVENT_PROPERTY_SET_PASSWORD_TOKEN => true
      }
    )
  end

  def http_authenticate
    auth_header = request.headers["BasicAuthorization"]
    raise AuthorizationHeaderError if auth_header.blank? || auth_header.split(" ").size != 2

    decoded_basic_auth = ::Base64.decode64(auth_header.split(" ").second)
    password = decoded_basic_auth&.split(":")&.second

    raise FetchResetPasswordSecretError if ENV["FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET"].blank?

    basic_auth_password = JSON.parse(ENV["FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET"])["password"]
    raise FetchResetPasswordSecretPasswordError if basic_auth_password.blank?

    render_forbidden if password != basic_auth_password
  rescue NoMethodError,
    AuthorizationHeaderError,
    FetchResetPasswordSecretError,
    FetchResetPasswordSecretPasswordError,
    JSON::ParserError => e
    Sentry.capture_exception(e)
    render_error
  end

  def multi_account_login_enabled?(subdomain)
    FeatureFlags::Queries::ValueForContext.new.call(
      flag_name: FeatureFlags::Flags::SUBDOMAIN_AWARE_LOGIN_ENABLED,
      subdomain: subdomain,
      fallback_value: false
    )
  end
end
